using UnityEngine;
using System;
using System.Collections.Generic;


public class Tilimanger : MonoBehaviour
{
    // 单例实例
    public static Tilimanger Instance { get; private set; }

    // 体力系统配置
    public int MAX_STAMINA = 100;
    public int DAILY_AD_LIMIT = 2;
    public int DAILY_SHARE_LIMIT = 1;
    public int AD_STAMINA_REWARD = 10;
    public int SHARE_STAMINA_REWARD = 5;

    // 玩家体力状态
    [Serializable]
    public class StaminaState
    {
        public int CurrentStamina;
        public string LastUpdated;
        public int DailyAdViews;
        public string LastAdViewDate;
        public int DailyShares;
        public string LastShareDate;
        public bool IsResettoday;
    }
    public Action<int> TiliChange;
    public Action<string> TiliTime;//时间倒计时
    // 当前体力状态
    public StaminaState PlayerStamina { get; private set; }

    // 最后一次服务器时间（用于模拟）
    private DateTime lastServerTime;

    // 自然恢复配置
    public int StaminaRecoveryIntervalMinutes = 5;
    public int StaminaRecoveryPointsPerInterval = 1;

    private void Awake()
    {
        // 单例模式
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);

            // 初始化体力状态

        }
        else
        {
            Destroy(gameObject);
        }
    }

    private void Start()
    {
        // 模拟从服务器获取时间（实际项目中需要网络请求）
        FetchServerTime();
        InitializeStaminaState();
        CheckStaminaRecovery();
    }
    private float _recoveryCheckInterval = 1.0f; // 每秒检测一次
    private float _timer;

    private void Update()
    {
        // 每帧更新时间（实际项目中应使用服务器时间）
        lastServerTime = lastServerTime.AddSeconds(Time.deltaTime);

        _timer += Time.deltaTime;
        if (_timer >= _recoveryCheckInterval)
        {
            CheckStaminaRecovery();
            _timer = 0f;
        }
    }

    // 初始化体力状态
    private void InitializeStaminaState()
    {
        if (!PlayerPrefs.HasKey("PlayerStamina"))
        {
            PlayerStamina = new StaminaState
            {
                CurrentStamina = MAX_STAMINA,
                LastUpdated = lastServerTime.ToString(),
                DailyAdViews = 0,
                LastAdViewDate = DateTime.MinValue.ToString(),
                DailyShares = 0,
                LastShareDate = DateTime.MinValue.ToString()
            };
        }
        else { LoadStaminaState(); }
    }
    // 加载保存的数据（如果有）



    // 模拟从服务器获取时间
    private void FetchServerTime()
    {
        GameObject loader = GameObject.Find("Loader");
        // loader.GetComponent<AADownloadManager2>().FetchServerTime();
        // 实际项目中这里应该是网络请求
        lastServerTime = DateTime.UtcNow.AddHours(8);
        Debug.Log($"获取服务器时间: {lastServerTime}");
    }

    // 检查体力恢复
    private void CheckStaminaRecovery()
    {
        DateTime currentTime = lastServerTime;

        // 1. 检查每日重置（凌晨4点）
        if (ShouldDailyReset(currentTime))
        {
            ResetDailyStamina(currentTime);
            Debug.Log("每日重置: 体力已恢复满值");
        }
        if (PlayerStamina.CurrentStamina == MAX_STAMINA)
        {
            // Debug.Log("体力满值");
            TiliTime?.Invoke(0.ToString());
            return;
        }
        else
        {
            //            Debug.Log("体力未满值" + PlayerStamina.CurrentStamina + currentTime + " currentTime" + PlayerStamina.LastUpdated + "LastUpdated ");

        }
        // 2. 检查自然恢复
        TimeSpan timeSinceLastUpdate = currentTime - DateTime.Parse(PlayerStamina.LastUpdated);
        int intervalsPassed = (int)(timeSinceLastUpdate.TotalMinutes / StaminaRecoveryIntervalMinutes);
        // Debug.Log($"当前时间: {currentTime}, 上次更新: {PlayerStamina.LastUpdated}, 间隔: {timeSinceLastUpdate.TotalMilliseconds}, 间隔数: {intervalsPassed}");
        TimeSpan timeSpan = TimeSpan.FromSeconds(300) - timeSinceLastUpdate;
        string timeUntilNextRecovery = GameTimeUtility.GameTimeUtils.FormatCountdown(timeSpan, GameTimeUtility.TimeFormat.Auto);
        //        Debug.Log($"距离下一次恢复还有: {timeUntilNextRecovery}");
        TiliTime?.Invoke(timeUntilNextRecovery);
        if (intervalsPassed > 0)
        {
            int staminaToAdd = intervalsPassed * StaminaRecoveryPointsPerInterval;
            int newStamina = Mathf.Min(PlayerStamina.CurrentStamina + staminaToAdd, MAX_STAMINA);

            if (newStamina > PlayerStamina.CurrentStamina)
            {
                PlayerStamina.CurrentStamina = newStamina;
                PlayerStamina.LastUpdated = currentTime.ToString();
                Debug.Log($"自然恢复: +{staminaToAdd}点体力, 当前: {PlayerStamina.CurrentStamina}" + "LastUpdated" + PlayerStamina.LastUpdated);

                // 保存状态
                SaveStaminaState();
                TiliChange?.Invoke(PlayerStamina.CurrentStamina);
                // 通知UI更新
                //UIManager.Instance.UpdateStaminaDisplay(PlayerStamina.CurrentStamina);
            }
        }
    }

    // 检查是否需要每日重置
    private bool ShouldDailyReset(DateTime currentTime)
    {
        // 检查日期是否变化（凌晨4点为重置点）
        DateTime lastResetCheck = DateTime.Parse(PlayerStamina.LastUpdated); ;
        DateTime resetTimeToday = new DateTime(
            currentTime.Year, currentTime.Month, currentTime.Day, 4, 0, 0);

        bool IsReset = currentTime >= resetTimeToday && lastResetCheck < resetTimeToday;
        //     Debug.Log("currentTime" + currentTime + " PlayerStamina.LastUpdated" + PlayerStamina.LastUpdated + "lastResetCheck" + lastResetCheck + "resetTimeToday" + resetTimeToday + "IsReset" + IsReset);
        // 如果当前时间在重置点之后，且上次更新在重置点之前
        return currentTime >= resetTimeToday && lastResetCheck < resetTimeToday;
    }

    // 执行每日重置
    private void ResetDailyStamina(DateTime resetTime)
    {
        PlayerStamina.CurrentStamina = MAX_STAMINA;
        PlayerStamina.DailyAdViews = 0;
        PlayerStamina.DailyShares = 0;
        PlayerStamina.LastUpdated = resetTime.ToString();
        PlayerStamina.IsResettoday = true;
        // 保存状态
        SaveStaminaState();
        TiliChange?.Invoke(PlayerStamina.CurrentStamina);
    }

    // 观看广告补充体力
    public bool AddStaminaFromAd()
    {
        DateTime currentTime = lastServerTime;

        // 检查是否已重置
        if (ShouldDailyReset(currentTime))
        {
            ResetDailyStamina(currentTime);
        }

        // 检查是否达到每日限制
        if (PlayerStamina.DailyAdViews >= DAILY_AD_LIMIT)
        {
            Debug.Log("已达到每日广告观看上限");
            return false;
        }

        // 补充体力
        PlayerStamina.CurrentStamina = Mathf.Min(PlayerStamina.CurrentStamina + AD_STAMINA_REWARD, MAX_STAMINA);
        PlayerStamina.DailyAdViews++;
        PlayerStamina.LastAdViewDate = currentTime.ToString();
        PlayerStamina.LastUpdated = currentTime.ToString();

        Debug.Log($"观看广告: +{AD_STAMINA_REWARD}点体力, 当前: {PlayerStamina.CurrentStamina}");

        // 保存状态
        SaveStaminaState();
        TiliChange?.Invoke(PlayerStamina.CurrentStamina);
        // 通知UI更新
        //  UIManager.Instance.UpdateStaminaDisplay(PlayerStamina.CurrentStamina);
        //  UIManager.Instance.UpdateAdCount(PlayerStamina.DailyAdViews);

        return true;
    }

    // 分享补充体力
    public bool AddStaminaFromShare()
    {
        DateTime currentTime = lastServerTime;

        // 检查是否已重置
        if (ShouldDailyReset(currentTime))
        {
            ResetDailyStamina(currentTime);
        }

        // 检查是否达到每日限制
        if (PlayerStamina.DailyShares >= DAILY_SHARE_LIMIT)
        {
            Debug.Log("已达到每日分享上限");
            return false;
        }

        // 补充体力
        PlayerStamina.CurrentStamina = Mathf.Min(PlayerStamina.CurrentStamina + SHARE_STAMINA_REWARD, MAX_STAMINA);
        PlayerStamina.DailyShares++;
        PlayerStamina.LastShareDate = currentTime.ToString();
        //  PlayerStamina.LastUpdated = currentTime;

        Debug.Log($"分享: +{SHARE_STAMINA_REWARD}点体力, 当前: {PlayerStamina.CurrentStamina}");

        // 保存状态
        SaveStaminaState();
        TiliChange?.Invoke(PlayerStamina.CurrentStamina);
        // 通知UI更新
        // UIManager.Instance.UpdateStaminaDisplay(PlayerStamina.CurrentStamina);
        //UIManager.Instance.UpdateShareCount(PlayerStamina.DailyShares);

        return true;
    }

    // 消耗体力
    public bool ConsumeStamina(int amount)
    {
        if (PlayerStamina.CurrentStamina < amount)
        {
            Debug.LogWarning($"体力不足! 需要: {amount}, 当前: {PlayerStamina.CurrentStamina}");
            return false;
        }

        PlayerStamina.CurrentStamina -= amount;
        // PlayerStamina.LastUpdated = lastServerTime.ToString();

        Debug.Log($"消耗体力: -{amount}点, 剩余: {PlayerStamina.CurrentStamina}");

        // 保存状态
        SaveStaminaState();

        // 通知UI更新
        // UIManager.Instance.UpdateStaminaDisplay(PlayerStamina.CurrentStamina);

        return true;
    }

    // 保存体力状态
    private void SaveStaminaState()
    {
        // 实际项目中应使用PlayerPrefs、文件存储或云存储
        string json = JsonUtility.ToJson(PlayerStamina);
        Debug.Log("LastUpdated" + PlayerStamina.LastUpdated);
        PlayerPrefs.SetString("PlayerStamina", json);
        PlayerPrefs.Save();
    }

    // 加载体力状态
    private void LoadStaminaState()
    {
        if (PlayerPrefs.HasKey("PlayerStamina"))
        {
            string json = PlayerPrefs.GetString("PlayerStamina");
            PlayerStamina = JsonUtility.FromJson<StaminaState>(json);
            Debug.Log("体力状态已加载" + PlayerStamina.CurrentStamina + "LastUpdated" + PlayerStamina.LastUpdated);
            // 检查是否需要重置
            if (ShouldDailyReset(lastServerTime))
            {
                ResetDailyStamina(lastServerTime);
            }

            Debug.Log("体力状态已加载");
        }
        else
        {
            Debug.Log("无保存的体力状态，使用默认值");
        }
        TiliChange?.Invoke(PlayerStamina.CurrentStamina);
    }

    // 获取当前体力状态（供其他系统使用）
    public StaminaState GetCurrentStaminaState()
    {
        // 返回副本防止外部修改
        return new StaminaState
        {
            CurrentStamina = PlayerStamina.CurrentStamina,
            //   LastUpdated = PlayerStamina.LastUpdated,
            DailyAdViews = PlayerStamina.DailyAdViews,
            LastAdViewDate = PlayerStamina.LastAdViewDate,
            DailyShares = PlayerStamina.DailyShares,
            LastShareDate = PlayerStamina.LastShareDate
        };
    }
}