using System.Collections;
using System.Collections.Generic;
using System.Threading.Tasks;
using Feif.UIFramework;
using UnityEngine;
using UnityEngine.UI;
using ZM.UGUIPro;


namespace Feif.UI
{
    public class TipWindowData : UIData
    {
    }

    [UIWindow]
    public class TipWindow : UIComponent<TipWindowData>
    {
        public ButtonPro buttonPro;
        protected override Task OnCreate()
        {
            return Task.CompletedTask;
        }

        protected override Task OnRefresh()
        {
            return Task.CompletedTask;
        }

        protected override void OnBind()
        {
            buttonPro.onClick.AddListener(() => { buttonProTip(); ; });
        }
        private void buttonProTip()
        {
            UIFrame.Hide<TipWindow>();
        }
        protected override void OnUnbind()
        {
            buttonPro.onClick.RemoveListener(() => { buttonProTip(); ; });
        }

        protected override void OnShow()
        {
        }

        protected override void OnHide()
        {
        }

        protected override void OnDied()
        {
        }
    }
}