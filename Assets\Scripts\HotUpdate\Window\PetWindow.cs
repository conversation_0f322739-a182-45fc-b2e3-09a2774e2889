using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading.Tasks;
using DG.Tweening;
using Feif.UIFramework;
using Spine.Unity;
using SuperScrollView;
using UnityEngine;
using UnityEngine.UI;

using ZM.UGUIPro;


namespace Feif.UI
{
    public class PetWindowData : UIData
    {
        public int itemIndex;
    }

    [UIWindow]
    public class PetWindow : UIComponent<PetWindowData>
    {
        public ButtonPro closeBtn;
        public ButtonPro ShopBtn;
        public Spine.Unity.SkeletonGraphic skeletonGraphic;

        public string[] animationNames;
        private float switchTimer = 0f;
        public float switchInterval = 3f; // 
        public GameObject shop;
        public List<ButtonPro> shopbuttonPro;
        public List<GameObject> shopbuttonPro1;
        public ButtonPro back;
        public ButtonPro TouchBtn;
        public ButtonPro FeedBtn;

        public ButtonPro Playbtn;
        public ButtonPro gamebtn;
        public GameObject idle;
        public GameObject catidletrans;
        public HexGridGo hexGridGo;
        public int num1;
        public int num2;
        public NestedListViewTopToBottomDemoScript nestedListViewTopToBottomDemoScript;

        // Animation state enum
        public enum PetAnimationState
        {
            None,
            Idle,
            Feed,// 吃饭
            Touch,  // 抚摸
            Play,   // 玩耍
            Play2,
            Stand,
            Idleshake,
            Idlelick,
            Idlesleep,
            leftwalk,
            rightwalk,

        }
        public enum PetBiaoqingState
        {
            None,
            happy,
            angry,
            Hungry,
            Hungry2,
            Ganga

        }


        // Current animation state
        private PetAnimationState currentState = PetAnimationState.None;
        private PetAnimationState previousState = PetAnimationState.None;
        private PetBiaoqingState currentbiaoqingState = PetBiaoqingState.None;
        private PetBiaoqingState previousbiaoqingState = PetBiaoqingState.None;

        public List<GameObject> cats; public List<GameObject> scrollViewslistcontent;
        public List<GameObject> zhuansi;

        protected override Task OnCreate()
        {
            return Task.CompletedTask;
        }

        protected override Task OnRefresh()
        {
            return Task.CompletedTask;
        }

        protected override void OnBind()
        {
            closeBtn.onClick.AddListener(() => { Close(); });
            ShopBtn.onClick.AddListener(() => { Shop(); });
            back.onClick.AddListener(() => { CloseShop(); });

            Playbtn.onClick.AddListener(() => { Play(); });
            TouchBtn.onClick.AddListener(() => { Touch(); });
            FeedBtn.onClick.AddListener(() => { Feed(); });
            gamebtn.onClick.AddListener(() => { playgame(); });
            DataManager.GetInstance().CurrencySanAction += SetCurrencySan;
            DataManager.GetInstance().CurrencyNumAction += Setcurrency;
            shopbuttonPro[0].onClick.AddListener(() => { Shop1(0); });
            shopbuttonPro[1].onClick.AddListener(() => { Shop1(1); });
            shopbuttonPro[2].onClick.AddListener(() => { Shop1(2); });
            shopbuttonPro[3].onClick.AddListener(() => { Shop1(3); });
            Tilimanger.Instance.TiliChange += TiliGet;
            Tilimanger.Instance.TiliTime += Tilitime;

        }
        public void ResetAnimation(PetAnimationState state, Spine.Unity.SkeletonGraphic skeletonGraphic, string animationName)
        {
            if (skeletonGraphic != null)
            {
                // Clear all animation tracks
                skeletonGraphic.AnimationState.ClearTracks();

                // Set skeleton back to setup pose
                skeletonGraphic.Skeleton.SetToSetupPose();

                // Force update to apply changes immediately
                skeletonGraphic.Update(0);

                Debug.Log("Animation reset to setup pose" + (int)(state));
                // skeletonGraphic.AnimationState.SetEmptyAnimation(0, 0.1f);
                skeletonGraphic.AnimationState.SetAnimation(0, animationName, true);
            }
        }
        public void ResetAnimation2(PetAnimationState state, Spine.Unity.SkeletonGraphic skeletonGraphic, string animationName)
        {
            if (skeletonGraphic != null)
            {
                // Clear all animation tracks
                skeletonGraphic.AnimationState.ClearTracks();

                // Set skeleton back to setup pose
                skeletonGraphic.Skeleton.SetToSetupPose();

                // Force update to apply changes immediately
                skeletonGraphic.Update(0);

                Debug.Log("Animation reset to setup pose" + (int)(state));
                // skeletonGraphic.AnimationState.SetEmptyAnimation(0, 0.1f);
                // skeletonGraphic.AnimationState.SetAnimation(0, animationName, true);
            }
        }

        protected override void OnUnbind()
        {
            closeBtn.onClick.RemoveListener(() => { Close(); });
            ShopBtn.onClick.RemoveListener(() => { Shop(); });
            back.onClick.RemoveListener(() => { CloseShop(); });

            Playbtn.onClick.RemoveListener(() => { Play(); });
            TouchBtn.onClick.RemoveListener(() => { Touch(); });
            FeedBtn.onClick.RemoveListener(() => { Feed(); });
            gamebtn.onClick.RemoveListener(() => { playgame(); });
            DataManager.GetInstance().CurrencySanAction -= SetCurrencySan;
            DataManager.GetInstance().CurrencyNumAction -= Setcurrency;
            shopbuttonPro[0].onClick.RemoveListener(() => { Shop1(0); });
            shopbuttonPro[1].onClick.RemoveListener(() => { Shop1(1); });
            shopbuttonPro[2].onClick.RemoveListener(() => { Shop1(2); });
            shopbuttonPro[3].onClick.RemoveListener(() => { Shop1(3); });
            Tilimanger.Instance.TiliChange -= TiliGet;
            Tilimanger.Instance.TiliTime -= Tilitime;
        }
        public void Setcurrency(int num)
        {

            startext.text = num.ToString();

        }
        public void SetCurrencySan(int mood, int health, int fullness)
        {
            moodtext.text = mood.ToString() + "/100";
            healthtext.text = health.ToString() + "/100"; ;
            fullnesstext.text = fullness.ToString() + "/100";
            Debug.Log("mood" + mood + "fullness" + fullness + "fullness_" + fullness / (float)50);
            moodprogress.DOFillAmount(mood / (float)100, 0.2f);
            healthprogress.DOFillAmount(health / (float)100, 0.2f);
            fullnessprogress.DOFillAmount(fullness / (float)100, 0.2f);

            if (mood > 5 && fullness > 5)
            {
                PlayBiaoqingByState(PetBiaoqingState.happy);

            }
            else if (fullness < 5 && mood > 5)
            {
                PlayBiaoqingByState(PetBiaoqingState.Hungry);
            }
            else if (mood < 5 && fullness > 5)
            {
                PlayBiaoqingByState(PetBiaoqingState.Hungry2);
            }
            else if (fullness < 5 && mood < 5)
            {
                int a = UnityEngine.Random.Range(0, 2);
                if (a == 1)
                {
                    PlayBiaoqingByState(PetBiaoqingState.Hungry);
                }
                else
                {
                    PlayBiaoqingByState(PetBiaoqingState.Hungry2);

                }

            }

        }
        public void TiliGet(int num)
        {
            TiLiprogress.DOFillAmount(num / (float)100, 0.2f);
            TiLitext.text = num.ToString() + "/100";
        }
        public void Tilitime(string time)
        {
            if (time == "0") { timegameobject.gameObject.SetActive(false); }
            else { timegameobject.gameObject.SetActive(true); }
            Timetext.text = time;
        }
        private async Task Shop()
        {


            // GameObject shopicon2 = await AAResources.LoadAssetAsync<GameObject>("Assets/Res/Prefabs/shop1.prefab");

            // shop = Instantiate(shopicon2, transform);
            // shopbuttonPro1 = shop.GetComponent<shop>().shopiconlist;
            // shopbuttonPro = shop.GetComponent<shop>().shopbuttonPro;
            // scrollViewslistcontent = shop.GetComponent<shop>().Scrowviewcontent;
            // startext = shop.GetComponent<shop>().startext;
            // back = shop.GetComponent<shop>().back;
            // starGameObject = shop.GetComponent<shop>().starGameObject;
            shop.SetActive(true);
            // shopbuttonPro[0].onClick.AddListener(() => { Shop1(0); });
            // shopbuttonPro[1].onClick.AddListener(() => { Shop1(1); });
            // shopbuttonPro[2].onClick.AddListener(() => { Shop1(2); });
            // shopbuttonPro[3].onClick.AddListener(() => { Shop1(3); });
            _ = Shop1(0);
        }
        private void playgame()
        {
            MusicMgr.GetInstance().PlaySound("点击.mp3", false);

            if (hexGridGo.levell != 0)
            {
                int staminaCost = GetStaminaCostByLevelDict(hexGridGo.levell);
                if (Tilimanger.Instance.GetCurrentStaminaState().CurrentStamina >= staminaCost)
                {

                    Tilimanger.Instance.ConsumeStamina(staminaCost);
                    Tilimanger.Instance.GetCurrentStaminaState();
                    UIFrame.Hide<PetWindow>();
                    UIFrame.Hide<StartPetWindow>();
                    if (!hexGridGo.transform.GetChild(0).gameObject.activeSelf)
                    {
                        hexGridGo.transform.GetChild(0).gameObject.SetActive(true);
                    }
                    hexGridGo.LevelGo(hexGridGo.levell, true, () =>
                    {


                        LoadLevelData(hexGridGo.levell);

                    });
                }
                else
                {
                    Debug.Log("体力不足");
                    TextGo("体力不足。", Color.red);
                }
            }
        }
        public GameObject Tiptext;
        public void TextGo(string text, Color color, Action action = null)
        {
            Tiptext.transform.GetChild(0).GetComponent<Text>().text = text;
            Tiptext.transform.GetChild(0).GetComponent<Text>().color = color;
            Tiptext.transform.localPosition = new Vector3(0, -517, 0);
            Tiptext.transform.DOKill();
            Tiptext.gameObject.SetActive(true);
            Tiptext.transform.DOLocalMoveY(-169, 2f).SetEase(Ease.OutBack).OnComplete(() =>
            {
                Tiptext.gameObject.SetActive(false);
                if (action != null)
                {
                    action.Invoke();
                }
            });
        }
        // 或者使用字典实现（更灵活）：
        private static readonly Dictionary<int, int> staminaCostMap = new Dictionary<int, int>
{
    {1, 5},   // 余数1 → 5体力
    {2, 10},  // 余数2 → 10体力
    {3, 10},  // 余数3 → 10体力
    {4, 15},  // 余数4 → 15体力
    {5, 10},  // 余数5 → 10体力
    {6, 5},   // 余数6 → 5体力
    {7, 10},  // 余数7 → 10体力
    {8, 10},  // 余数8 → 10体力
    {9, 15},  // 余数9 → 15体力
    {0, 10}   // 余数0 → 10体力
};
        private int GetStaminaCostByLevelDict(int levelId)
        {
            int remainder = levelId % 10;
            if (staminaCostMap.TryGetValue(remainder, out int cost))
            {
                return cost;
            }
            return 10; // 默认值
        }
        void LoadLevelData(int levelnum)
        {
            LevelData level = LevelConfig.Instance.GetLevel(levelnum);
            if (level != null)
            {
                Loadnum(levelnum);

                GameWindowData gamePanelData = new GameWindowData()
                {
                    height = Demo2Launcher.GetInstance().Theheight,
                    width = Demo2Launcher.GetInstance().Thewidth,
                    num1 = num1,
                    num2 = num2 - 1,
                    levelData = level
                };

                UIFrame.Show<GameWindow>(gamePanelData);
                //  UIFrame.Hide<PetWindow>();
            }
        }
        private void Loadnum(int num)
        {


            var (tens, ones) = SplitNumber(num + 1);
            if (ones == 0)
            {
                num1 = tens;
                num2 = 10;

            }
            else
            {
                num1 = tens + 1;
                num2 = ones;

            }


        }
        private (int tens, int ones) SplitNumber(int number)
        {
            int tens = number / 10;    // 获取十位数：49 / 10 = 4
            int ones = number % 10;    // 获取个位数：49 % 10 = 9
            return (tens, ones);
        }
        private async Task Shop1(int index)
        {
            DataManager.GetInstance().CurrencyNumAction?.Invoke(DataManager.GetInstance().CurrencyNum);
            if (index == 0)
            {
                starGameObject.SetActive(true);
                shopbuttonPro1[2].SetActive(false);
                shopbuttonPro1[3].SetActive(true);
                shopbuttonPro1[0].SetActive(true);
                shopbuttonPro1[1].SetActive(false);

                shopbuttonPro1[4].SetActive(false);

                shopbuttonPro1[5].SetActive(true);
                shopbuttonPro1[6].SetActive(false);

                shopbuttonPro1[7].SetActive(true);
                List<CatItemData> foodItems = ItemConfig.Instance.GetItemsByCategory("食物");
                if (foodItems != null)
                {
                    if (!shop0)
                    {
                        shop0 = true;
                        foreach (CatItemData item in foodItems)
                        {
                            if (item.description != "默认")

                            {

                                GameObject shopicon = await AAResources.LoadAssetAsync<GameObject>("shopicon");
                                GameObject shopicon2 = Instantiate(shopicon);
                                shopicon2.transform.SetParent(scrollViewslistcontent[index].transform);
                                Debug.Log(item.name + "item");
                                _ = shopicon2.GetComponent<shopicon>().initAsync(item);
                            }

                        }
                    }
                }
            }
            else if (index == 1)
            {
                starGameObject.SetActive(true);
                shopbuttonPro1[0].SetActive(false);
                shopbuttonPro1[1].SetActive(true);
                shopbuttonPro1[2].SetActive(true);
                shopbuttonPro1[3].SetActive(false);

                shopbuttonPro1[4].SetActive(false);

                shopbuttonPro1[5].SetActive(true);
                shopbuttonPro1[6].SetActive(false);

                shopbuttonPro1[7].SetActive(true);
                List<CatItemData> windowItems = ItemConfig.Instance.GetItemsByCategory("装饰");
                if (windowItems != null)
                {
                    if (!shop1)
                    {
                        shop1 = true;
                        foreach (CatItemData item in windowItems)
                        {
                            if (item.description != "默认")

                            {

                                GameObject shopicon = await AAResources.LoadAssetAsync<GameObject>("shopicon");
                                GameObject shopicon2 = Instantiate(shopicon);
                                shopicon2.transform.SetParent(scrollViewslistcontent[index].transform);
                                Debug.Log(item.name + "item");
                                _ = shopicon2.GetComponent<shopicon>().initAsync(item);
                            }
                        }
                    }
                }
            }
            else if (index == 2)
            {
                starGameObject.SetActive(true);
                shopbuttonPro1[0].SetActive(false);
                shopbuttonPro1[1].SetActive(true);
                shopbuttonPro1[4].SetActive(true);
                shopbuttonPro1[5].SetActive(false);

                shopbuttonPro1[2].SetActive(false);

                shopbuttonPro1[3].SetActive(true);
                shopbuttonPro1[6].SetActive(false);

                shopbuttonPro1[7].SetActive(true);
                List<CatItemData> playItems = ItemConfig.Instance.GetItemsByCategory("玩具");
                if (playItems != null)
                {
                    if (!shop2)
                    {
                        shop2 = true;
                        foreach (CatItemData item in playItems)
                        {
                            if (item.description != "默认")

                            {

                                GameObject shopicon = await AAResources.LoadAssetAsync<GameObject>("shopicon");
                                GameObject shopicon2 = Instantiate(shopicon);
                                shopicon2.transform.SetParent(scrollViewslistcontent[index].transform);
                                Debug.Log(item.name + "item");
                                _ = shopicon2.GetComponent<shopicon>().initAsync(item);
                            }
                        }
                    }
                }
            }
            else if (index == 3)
            {
                starGameObject.SetActive(false);
                shopbuttonPro1[0].SetActive(false);
                shopbuttonPro1[1].SetActive(true);
                shopbuttonPro1[6].SetActive(true);
                shopbuttonPro1[7].SetActive(false);

                shopbuttonPro1[2].SetActive(false);

                shopbuttonPro1[3].SetActive(true);
                shopbuttonPro1[4].SetActive(false);

                shopbuttonPro1[5].SetActive(true);

                // 清空现有内容
                // int index2 = 0;
                // foreach (Transform child in scrollViewslistcontent[index].transform)
                // {
                //     index2++;
                //     if (index2 > 2)


                //         Destroy(child.gameObject);
                // }
                nestedListViewTopToBottomDemoScript.Refresh();

                // 显示已购买的物品
                // _ = ShowPurchasedItemsAsync(index);
            }
        }
        private bool shop0;
        private bool shop1;
        private bool shop2;

        public void CloseShop()
        {
            shop.SetActive(false);
        }
        Dictionary<PetAnimationState, Spine.Unity.SkeletonGraphic> Dicanimatenames = new Dictionary<PetAnimationState, Spine.Unity.SkeletonGraphic>();
        Dictionary<PetBiaoqingState, GameObject> Dicbiaoqingnames = new Dictionary<PetBiaoqingState, GameObject>();


        /// <summary>
        /// 为猫对象列表添加动画组件
        /// </summary>
        /// <param name="cats">包含猫对象的列表</param>
        /// <remarks>
        /// 遍历猫对象列表，将每个猫对象的动画组件添加到字典中（如果字典中不存在该动画名称）
        /// </remarks>
        private void AddAnimate(List<GameObject> cats)
        {
            foreach (var item in cats)
            {
                if (!Dicanimatenames.ContainsKey(item.GetComponent<Thecatsidle>().thecatsidle))
                    Dicanimatenames.Add(item.GetComponent<Thecatsidle>().thecatsidle, item.GetComponent<Spine.Unity.SkeletonGraphic>());
            }

        }
        private void Addbiaoqing(List<GameObject> biaoqing)
        {
            foreach (var item in biaoqing)
            {
                if (!Dicbiaoqingnames.ContainsKey(item.GetComponent<Thecatsidle>().thecatbiaoqingstate))
                    Dicbiaoqingnames.Add(item.GetComponent<Thecatsidle>().thecatbiaoqingstate, item);
            }

        }
        private Spine.Unity.SkeletonGraphic GetAnimate(PetAnimationState state)
        {
            if (Dicanimatenames.ContainsKey(state))
            {
                return Dicanimatenames[state];
            }

            return null;
        }
        private GameObject Getbiaoqing(PetBiaoqingState state)
        {
            if (Dicbiaoqingnames.ContainsKey(state))
            {
                return Dicbiaoqingnames[state];
            }

            return null;
        }
        public Tweener movementTween;
        public Tweener movementTween2;
        public Text moodtext;
        public Text healthtext;
        public Text TiLitext;
        public Text fullnesstext;
        public Image moodprogress;
        public Image healthprogress;
        public Image fullnessprogress; public Image TiLiprogress;
        public Text Timetext;
        public GameObject timegameobject;

        // Play animation based on state
        public void PlayAnimationByState(PetAnimationState state)
        {
            previousState = currentState;
            currentState = state;
            Debug.Log("currentState" + currentState + " " + previousState + " previousState");


            //Debug.Log("i" + i + cats[i].name + "初始状态：完全透明2");
            var skeletonGraphic = GetAnimate(currentState);
            var skeletonGraphic2 = GetAnimate(previousState);

            // 初始状态：完全透明
            //skeletonGraphic.color = new Color(1, 1, 1, 0);  // RGB=白色, Alpha=0

            // 使用 DOTween 渐变到不透明（Alpha=1）
            if (previousState == currentState)
            {
                if (skeletonGraphic2 != null)
                {
                    skeletonGraphic2.DOColor(new Color(1, 1, 1, 0), 0f)  // 目标颜色（白色+不透明），持续时间1秒
                                   .SetEase(Ease.InOutQuad);
                }

                skeletonGraphic.DOColor(new Color(1, 1, 1, 1), 0f)  // 目标颜色（白色+不透明），持续时间1秒
                              .SetEase(Ease.InOutQuad);
            }
            else
            {
                if (skeletonGraphic2 != null)
                {
                    skeletonGraphic2.DOColor(new Color(1, 1, 1, 0), 0.5f)  // 目标颜色（白色+不透明），持续时间1秒
                                   .SetEase(Ease.InOutQuad);
                }
                skeletonGraphic.DOColor(new Color(1, 1, 1, 1), 0.5f)  // 目标颜色（白色+不透明），持续时间1秒
                                   .SetEase(Ease.InOutQuad);
            }
            if (currentState != PetAnimationState.Idle)
            {
                ResetAnimation(state, skeletonGraphic, skeletonGraphic.GetComponent<Thecatsidle>().thecatsidlename);
                if (movementTween != null)
                    movementTween.Kill(); if (movementTween2 != null)
                    movementTween2.Kill();
                if (currentState == PetAnimationState.leftwalk)
                {
                    skeletonGraphic.GetComponent<Thecatsidle>().biaoqing.transform.localPosition = new Vector3(-25, -207.4f, 8.7f);

                    //skeletonGraphic.GetComponent<Thecatsidle>().biaoqing.transform.SetParent(skeletonGraphic.transform)

                    skeletonGraphic.transform.rotation = Quaternion.Euler(0, 0, 0);
                    movementTween = skeletonGraphic.transform.DOLocalMoveX(-590f, 5f).SetEase(Ease.Linear).OnComplete(() =>
                         {
                             skeletonGraphic.transform.rotation = Quaternion.Euler(180, 0, 180);
                             skeletonGraphic.transform.DOLocalMoveX(0, 5.1f).SetEase(Ease.Linear).OnComplete(() =>
                             {
                             });
                         }); movementTween2 = skeletonGraphic.GetComponent<Thecatsidle>().biaoqing.transform.DOLocalMoveX(-590f, 5f).SetEase(Ease.Linear).OnComplete(() =>
                         {
                             // skeletonGraphic.transform.rotation = Quaternion.Euler(180, 0, 180);
                             skeletonGraphic.GetComponent<Thecatsidle>().biaoqing.transform.DOLocalMoveX(0, 5.1f).SetEase(Ease.Linear).OnComplete(() =>
                               {
                               });
                         });
                }
                else if (currentState == PetAnimationState.rightwalk)
                {
                    skeletonGraphic.GetComponent<Thecatsidle>().biaoqing.transform.localPosition = new Vector3(-25, -207.4f, 8.7f);


                    //   skeletonGraphic.GetComponent<Thecatsidle>().biaoqing.transform.SetParent(skeletonGraphic.transform);

                    skeletonGraphic.transform.rotation = Quaternion.Euler(180, 0, 180);
                    movementTween = skeletonGraphic.transform.DOLocalMoveX(590f, 5f).SetEase(Ease.Linear).OnComplete(() =>
                 {
                     skeletonGraphic.transform.rotation = Quaternion.Euler(0, 0, 0);
                     skeletonGraphic.transform.DOLocalMoveX(0, 5.1f).SetEase(Ease.Linear).OnComplete(() =>
                     {

                     }); ;
                 });
                    movementTween2 = skeletonGraphic.GetComponent<Thecatsidle>().biaoqing.transform.DOLocalMoveX(590f, 5f).SetEase(Ease.Linear).OnComplete(() =>
                            {
                                // skeletonGraphic.transform.rotation = Quaternion.Euler(180, 0, 180);
                                skeletonGraphic.GetComponent<Thecatsidle>().biaoqing.transform.DOLocalMoveX(0, 5.1f).SetEase(Ease.Linear).OnComplete(() =>
                                  {
                                  });
                            });
                }
                else
                {
                    skeletonGraphic.GetComponent<Thecatsidle>().biaoqing.transform.localPosition = new Vector3(-25, -207.4f, 8.7f);

                    // skeletonGraphic.GetComponent<Thecatsidle>().biaoqing.transform.SetParent(catidletrans.transform);


                }
            }
            DataManager.GetInstance().CurrencySanAction?.Invoke(DataManager.GetInstance().mood, DataManager.GetInstance().health, DataManager.GetInstance().fullness);



        }
        public void PlayBiaoqingByState(PetBiaoqingState state)
        {
            previousbiaoqingState = currentbiaoqingState;
            currentbiaoqingState = state;
            Debug.Log("currentbiaoqingState" + currentbiaoqingState + " " + previousbiaoqingState + " previousbiaoqingState");


            //Debug.Log("i" + i + cats[i].name + "初始状态：完全透明2");
            GameObject biaoing = Getbiaoqing(currentbiaoqingState);
            GameObject biaoing2 = Getbiaoqing(previousbiaoqingState);


            if (currentState == PetAnimationState.Idlesleep)
            {
                ///睡觉时候不显示
                if (biaoing2 != null)
                {
                    biaoing2.GetComponent<Image>().DOColor(new Color(1, 1, 1, 0), 0.5f)  // 目标颜色（白色+不透明），持续时间1秒
                                 .SetEase(Ease.InOutQuad);
                    if (biaoing2.transform.childCount > 0)
                    {
                        biaoing2.transform.GetChild(0).GetComponent<Image>().DOColor(new Color(1, 1, 1, 0), 0.5f)  // 目标颜色（白色+不透明），持续时间1秒
                        .SetEase(Ease.InOutQuad);
                    }
                } ///睡觉时候不显示
                // if (biaoing != null)
                // {
                biaoing.GetComponent<Image>().DOColor(new Color(1, 1, 1, 0), 0.5f)  // 目标颜色（白色+不透明），持续时间1秒
                              .SetEase(Ease.InOutQuad);
                if (biaoing.transform.childCount > 0)
                {
                    biaoing.transform.GetChild(0).GetComponent<Image>().DOColor(new Color(1, 1, 1, 0), 0.5f)  // 目标颜色（白色+不透明），持续时间1秒
                    .SetEase(Ease.InOutQuad);
                }
                // }
            }
            else
            {
                if (currentbiaoqingState == PetBiaoqingState.Hungry)
                {
                    biaoing.transform.GetChild(0).GetComponent<Image>().DOColor(new Color(1, 1, 1, 1), 0.5f)  // 目标颜色（白色+不透明），持续时间1秒
                                   .SetEase(Ease.InOutQuad);
                }
                if (previousbiaoqingState == currentbiaoqingState)
                {
                    if (biaoing2 != null)
                    {
                        biaoing2.GetComponent<Image>().DOColor(new Color(1, 1, 1, 0), 0f)  // 目标颜色（白色+不透明），持续时间1秒
                                       .SetEase(Ease.InOutQuad);
                        if (biaoing2.transform.childCount > 0)
                        {
                            biaoing.transform.GetChild(0).GetComponent<Image>().DOColor(new Color(1, 1, 1, 0), 0.5f)  // 目标颜色（白色+不透明），持续时间1秒
                            .SetEase(Ease.InOutQuad);
                        }
                    }

                    biaoing.GetComponent<Image>().DOColor(new Color(1, 1, 1, 1), 0f)  // 目标颜色（白色+不透明），持续时间1秒
                                 .SetEase(Ease.InOutQuad).OnComplete(() =>
                                 {
                                     Sequence loopSequence = DOTween.Sequence();

                                     // Add the up and down movements to the sequence
                                     loopSequence.Append(biaoing.transform.DOLocalMoveY(630f, 0.5f).SetEase(Ease.Linear)).Append(biaoing.transform.DOLocalMoveY(597.4f, 0.5f).SetEase(Ease.Linear))
                                               ;

                                     // Set the sequence to loop infinitely
                                     loopSequence.SetLoops(-1); // -1 means infinite loops

                                     // Play the sequence
                                     loopSequence.Play();
                                 });
                }
                else
                {
                    if (biaoing2 != null)
                    {
                        biaoing2.GetComponent<Image>().DOColor(new Color(1, 1, 1, 0), 0.5f)  // 目标颜色（白色+不透明），持续时间1秒
                                     .SetEase(Ease.InOutQuad);
                        if (biaoing2.transform.childCount > 0)
                        {
                            biaoing.transform.GetChild(0).GetComponent<Image>().DOColor(new Color(1, 1, 1, 0), 0.5f)  // 目标颜色（白色+不透明），持续时间1秒
                            .SetEase(Ease.InOutQuad);
                        }
                    }
                    biaoing.GetComponent<Image>().DOColor(new Color(1, 1, 1, 1), 0.5f)  // 目标颜色（白色+不透明），持续时间1秒
                                    .SetEase(Ease.InOutQuad).OnComplete(() =>
                                 {
                                     Sequence loopSequence = DOTween.Sequence();

                                     // Add the up and down movements to the sequence
                                     loopSequence.Append(biaoing.transform.DOLocalMoveY(630f, 0.5f).SetEase(Ease.Linear)).Append(biaoing.transform.DOLocalMoveY(597.4f, 0.5f).SetEase(Ease.Linear))
                                              ;

                                     // Set the sequence to loop infinitely
                                     loopSequence.SetLoops(-1); // -1 means infinite loops

                                     // Play the sequence
                                     loopSequence.Play();
                                 }); ;
                }
            }




        }

        // Example methods to trigger specific states
        public void Play()
        {
            MusicMgr.GetInstance().PlaySound("点击.mp3", false);
            Image toyImage = Playbtn.transform.GetChild(0).GetComponent<Image>();
            string spriteName = toyImage.sprite.name;

            // Extract item ID from sprite name
            string itemId = spriteName.Replace("itemicon", "");
            Debug.Log("Play" + movementTween);
            if (movementTween != null)
                movementTween.Kill();
            if (movementTween2 != null)
                movementTween2.Kill();
            catidletrans.gameObject.SetActive(false);
            if (itemId == "310001")
            {
                PlayAnimationByState(PetAnimationState.Play);
            }
            else if (itemId == "310002")
            {
                PlayAnimationByState(PetAnimationState.Play2);
            }
            else if (itemId == "310003")
            {
                PlayAnimationByState(PetAnimationState.Stand);
            }
            // Get the toy item image sprite


            // Get item data
            CatItemData itemData = ItemConfig.Instance.GetItemById(itemId);
            if (itemData != null)
            {
                // Apply mood and health attributes
                DataManager.GetInstance().AddStatusValue("mood", itemData.mood);
                DataManager.GetInstance().AddStatusValue("health", itemData.health);

                Debug.Log($"Applied toy effects: Mood +{itemData.mood}, Health +{itemData.health}");
            }

            TimeManager.GetInstance().StopWait();
            TimeManager.GetInstance().Wait(2.6f, () =>
            {
                Reset();
                Idle();
            });
        }

        public void Idle()
        {
            catidletrans.gameObject.SetActive(true);
            //PlayAnimationByState(PetAnimationState.Idle);
            PlayRandomAnimation();
        }

        public void Touch()
        {
            MusicMgr.GetInstance().PlaySound("点击.mp3", false);
            Debug.Log("Touch" + movementTween);
            if (movementTween != null)
                movementTween.Kill();
            if (movementTween2 != null)
                movementTween2.Kill();
            catidletrans.gameObject.SetActive(false);

            PlayAnimationByState(PetAnimationState.Touch);

            // Increase mood by 1 when touching the pet
            DataManager.GetInstance().AddStatusValue("mood", 1);
            Debug.Log("Pet touched: Mood +1");

            TimeManager.GetInstance().StopWait();
            TimeManager.GetInstance().Wait(2.6f, () =>
            {
                Reset();
                Idle();
            });
        }

        public void Feed()
        {
            MusicMgr.GetInstance().PlaySound("点击.mp3", false);
            Debug.Log("Feed" + movementTween);
            if (movementTween != null)
                movementTween.Kill();
            if (movementTween2 != null)
                movementTween2.Kill();
            catidletrans.gameObject.SetActive(false);

            PlayAnimationByState(PetAnimationState.Feed);

            // Get the food item image sprite
            Image foodImage = zhuansi[10].GetComponent<Image>();
            string spriteName = foodImage.sprite.name;

            // Extract item ID from sprite name (assuming sprite name contains the item ID)
            string itemId = spriteName.Replace("itemicon", "");

            // Get item data
            CatItemData itemData = ItemConfig.Instance.GetItemById(itemId);
            if (itemData != null)
            {
                // Apply mood and fullness attributes
                DataManager.GetInstance().AddStatusValue("mood", itemData.mood);
                DataManager.GetInstance().AddStatusValue("fullness", itemData.health);
                //  DataManager.GetInstance().AddStatusValue("health", itemData.health);


                Debug.Log($"Applied food effects: Mood +{itemData.mood}, Fullness +{itemData.value}");
            }

            TimeManager.GetInstance().StopWait();
            TimeManager.GetInstance().Wait(2.6f, () =>
            {
                Reset();
                Idle();
            });
        }




        private void Close()
        {
            UIFrame.Hide<PetWindow>();
            //UIFrame.Show<MainPanel>();
        }
        public void share()
        {
            Tilimanger.Instance.AddStaminaFromShare();
        }
        public void AddStaminaFromAd()
        {
            Tilimanger.Instance.AddStaminaFromAd();
        }
        protected override void OnShow()
        {

            Tilimanger.Instance.TiliChange(Tilimanger.Instance.GetCurrentStaminaState().CurrentStamina);
            GameObject HexGridGo = GameObject.Find("Hex Grid");
            LoadZhuansi();

            hexGridGo = HexGridGo.GetComponent<HexGridGo>();
            int staminaCost = GetStaminaCostByLevelDict(hexGridGo.levell);
            textlevelcost.text = "×" + staminaCost.ToString();
            _ = instantitiecatAsync();
            //  GameObject cat = AAResources.LoadAssetAsync<GameObject>("cat_" + index.ToString());
            //  GameObject cat1 = Instantiate(cat, idle.transform);
            //AddAnimate(cats);

            // PlayRandomAnimation();
        }
        private void LoadZhuansi()
        {
            string food = PlayerPrefs.GetString("IconitemIndex食物");
            string play = PlayerPrefs.GetString("IconitemIndex玩具");
            string zhuansi = PlayerPrefs.GetString("IconitemIndex装饰饮水");
            string zhuansi1 = PlayerPrefs.GetString("IconitemIndex装饰窝");
            string zhuansi2 = PlayerPrefs.GetString("IconitemIndex装饰地毯");
            string zhuansi3 = PlayerPrefs.GetString("IconitemIndex装饰地板");
            string zhuansi4 = PlayerPrefs.GetString("IconitemIndex装饰床");
            string zhuansi5 = PlayerPrefs.GetString("IconitemIndex装饰窗户");
            string zhuansi6 = PlayerPrefs.GetString("IconitemIndex装饰墙面");
            string zhuansi7 = PlayerPrefs.GetString("IconitemIndex装饰盆栽");
            string zhuansi8 = PlayerPrefs.GetString("IconitemIndex装饰墙画");

            UseItem(food);
            UseItem(play);
            UseItem(zhuansi);
            UseItem(zhuansi1);
            UseItem(zhuansi2);
            UseItem(zhuansi3);
            UseItem(zhuansi4);
            UseItem(zhuansi5);
            UseItem(zhuansi6);
            UseItem(zhuansi7);
            UseItem(zhuansi8);
        }
        GameObject cat;
        public Text textlevelcost;
        public Text startext;
        public GameObject starGameObject;

        private async Task instantitiecatAsync()
        {
            int index = Data.itemIndex + 1;
            GameObject catPrefab = await AAResources.LoadAssetAsync<GameObject>("cat_" + index.ToString());
            cat = Instantiate(catPrefab, idle.transform);
            cat.transform.localPosition = Vector3.zero;
            cat.transform.localScale = Vector3.one;
            cat.transform.localRotation = Quaternion.identity;
            cats = cat.GetComponent<animal>().cats;
            catidletrans = cat.GetComponent<animal>().idletrans;
            AddAnimate(cats);
            Addbiaoqing(cat.GetComponent<animal>().biaoqing);
            Idle();
        }


        protected override void OnHide()
        {
        }

        protected override void OnDied()
        {
        }
        public void Reset()
        {
            if (movementTween != null)
                movementTween.Kill();
            if (movementTween2 != null)
                movementTween2.Kill();
            foreach (var item in cats)
            {

                item.SetActive(true);
                item.GetComponent<Spine.Unity.SkeletonGraphic>().color = new Color(1, 1, 1, 0);
                Debug.Log("初始状态：完全透明2" + item.name + "thecatsidle " + item.GetComponent<Thecatsidle>().thecatsidle + " skeletonGraphic" + item.GetComponent<Spine.Unity.SkeletonGraphic>().name + item.GetComponent<Thecatsidle>().thecatsidlename + "thecatsidlename ");
                ResetAnimation2(item.GetComponent<Thecatsidle>().thecatsidle, item.GetComponent<Spine.Unity.SkeletonGraphic>(), item.GetComponent<Thecatsidle>().thecatsidlename);
                item.GetComponent<Spine.Unity.SkeletonGraphic>().transform.DOKill();

                if (item.GetComponent<Thecatsidle>().thecatsidle == PetAnimationState.leftwalk)
                {
                    item.transform.localRotation = Quaternion.Euler(0, 0, 0);
                    item.transform.localPosition = new Vector3(-22, -130, 4.7f);
                }
                else if (item.GetComponent<Thecatsidle>().thecatsidle == PetAnimationState.rightwalk)
                {
                    item.transform.localRotation = Quaternion.Euler(180, 0, 180);
                    item.transform.localPosition = new Vector3(-22, -130, 4.7f);
                }

            }
        }

        private void PlayRandomAnimation()
        {
            switchTimer = 0f;
            int randomIndex = UnityEngine.Random.Range(0, animationNames.Length);
            Debug.Log(randomIndex + "randomIndex");
            switchInterval = 6f;
            if (randomIndex == 0 || randomIndex == 1)
            {

                PlayAnimationByState(PetAnimationState.Idle);
                skeletonGraphic = GetAnimate(PetAnimationState.Idle);
                if (skeletonGraphic != null && animationNames != null && animationNames.Length > 0)
                {
                    // Select random animation from the array
                    string randomAnim = animationNames[randomIndex];

                    // Play the animation
                    //                Debug.Log(randomAnim + "randomAnim");
                    //   skeletonGraphic.AnimationState.SetEmptyAnimation(0, 0.1f);
                    skeletonGraphic.AnimationState.SetAnimation(0, randomAnim, true);

                }
            }
            else if (randomIndex == 2)
            {
                PlayAnimationByState(PetAnimationState.Idleshake);
            }
            else if (randomIndex == 3)
            {
                PlayAnimationByState(PetAnimationState.Idlelick);
            }
            else if (randomIndex == 4)
            {
                switchInterval = 16f;
                PlayAnimationByState(PetAnimationState.Idlesleep);
            }
            else if (randomIndex == 5)
            {
                switchInterval = 10f;
                PlayAnimationByState(PetAnimationState.leftwalk);
            }
            else if (randomIndex == 6)
            {
                switchInterval = 10f;
                PlayAnimationByState(PetAnimationState.rightwalk);
            }
        }
        private void Update()
        {
            // Switch animation periodically
            switchTimer += Time.deltaTime;
            if (switchTimer >= switchInterval)
            {
                PlayRandomAnimation();
                switchTimer = 0f;
            }
        }



        // 新增方法：显示已购买的物品
        private async Task ShowPurchasedItemsAsync(int index)
        {
            List<string> purchasedItemIds = ItemManager.Instance.purchasedItems;

            if (purchasedItemIds.Count > 0)
            {
                foreach (string itemId in purchasedItemIds)
                {
                    // 获取物品数据
                    CatItemData itemData = ItemConfig.Instance.GetItemById(itemId);
                    if (itemData != null)
                    {
                        string category = itemData.category;

                        // // 初始化类别列表（如果不存在）
                        // if (!itemsByCategory.ContainsKey(category))
                        // {
                        //     itemsByCategory[category] = new List<ItemData>();
                        //     categoryCount[category] = 0;
                        // }
                        // GameObject shopicon = await AAResources.LoadAssetAsync<GameObject>("shopicon");
                        // GameObject shopicon2 = Instantiate(shopicon);
                        // shopicon2.transform.SetParent(scrollViewslistcontent[index].transform);
                        // shopicon2.transform.localScale = Vector3.one;

                        // // 初始化物品图标
                        // var iconComponent = shopicon2.GetComponent<shopicon>();
                        // await iconComponent.initAsync(itemData);

                        // // 修改按钮文本和状态
                        // var buyBtn = iconComponent.BuyBtn;
                        // buyBtn.transform.GetChild(0).GetComponent<Text>().text = "使用";
                        // buyBtn.onClick.AddListener(() => { UseItem(itemId); });
                        // buyBtn.GetComponent<Image>().color = Color.white;
                        // buyBtn.GetComponent<ButtonPro>().interactable = true;
                    }
                }
            }
            else
            {
                // 如果没有已购买的物品，显示提示
                GameObject emptyText = new GameObject("EmptyText");
                emptyText.transform.SetParent(scrollViewslistcontent[index].transform);
                emptyText.transform.localScale = Vector3.one;

                Text text = emptyText.AddComponent<Text>();
                text.text = "暂无已购买的物品";
                text.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
                text.fontSize = 24;
                text.alignment = TextAnchor.MiddleCenter;
                text.color = Color.gray;

                RectTransform rectTransform = text.GetComponent<RectTransform>();
                rectTransform.anchorMin = new Vector2(0, 0);
                rectTransform.anchorMax = new Vector2(1, 1);
                rectTransform.sizeDelta = Vector2.zero;
                rectTransform.anchoredPosition = Vector2.zero;
            }
        }

        public void UseItem(string itemId)
        {
            // 获取物品数据
            CatItemData itemData = ItemConfig.Instance.GetItemById(itemId);
            if (itemData == null)
            {
                Debug.LogError($"找不到ID为 {itemId} 的物品数据");
                return;
            }
            string category = itemData.category;
            // 获取物品的小类别
            string subCategory = itemData.subCategory;
            Debug.Log($"使用物品: {itemData.name}, 大类别: {subCategory}");

            // 根据不同的小类别执行不同的操作
            switch (category)
            {
                case "食物":
                    // 喂食宠物
                    //  Feed();
                    _ = ApplyFeedAsync(itemId, subCategory);
                    break;

                case "玩具":
                    // 与宠物玩耍
                    //Play();
                    _ = ApplyplayAsync(itemId, subCategory);
                    break;

                case "装饰":
                    // 应用装饰物品
                    _ = ApplyDecorationAsync(itemId, subCategory);
                    break;

                case "宠物商店折扣券":
                    // 应用折扣
                    ApplyDiscount(itemData.value);
                    break;

                default:
                    Debug.Log($"未知的物品小类别: {subCategory}");
                    break;
            }

            // 可以在这里添加使用物品后的效果，比如增加宠物的健康值或心情值
            if (itemData.health > 0)
            {
                // 增加宠物健康值
                Debug.Log($"宠物健康值增加: {itemData.health}");
            }

            if (itemData.mood > 0)
            {
                // 增加宠物心情值
                Debug.Log($"宠物心情值增加: {itemData.mood}");
            }
        }

        private async Task ApplyplayAsync(string itemId, string subCategory)
        {
            Playbtn.transform.GetChild(0).GetComponent<Image>().sprite = await AAResources.LoadAssetAsync<Sprite>(itemId + "itemicon");
        }

        private async Task ApplyFeedAsync(string itemId, string subCategory)
        {
            // FeedBtn.transform.GetChild(0).GetComponent<Image>().text = "使用中...";
            zhuansi[10].GetComponent<Image>().sprite = await AAResources.LoadAssetAsync<Sprite>(itemId + "itemicon");
        }

        // 应用装饰物品
        private async Task ApplyDecorationAsync(string itemId, string subCategory)
        {
            Debug.Log($"应用装饰物品: {itemId}");
            // 这里可以实现装饰物品的逻辑
            // 例如更改背景、添加装饰物等
            if (subCategory == "地毯")
            {
                // 更改背景
                zhuansi[3].GetComponent<Image>().sprite = await AAResources.LoadAssetAsync<Sprite>(itemId + "itemicon");
            }
            else if (subCategory == "地板")
            {
                // 添加装饰物
                zhuansi[0].GetComponent<Image>().sprite = await AAResources.LoadAssetAsync<Sprite>(itemId + "itemicon");
            }
            else if (subCategory == "床")
            {
                // 添加装饰物
                zhuansi[6].GetComponent<Image>().sprite = await AAResources.LoadAssetAsync<Sprite>(itemId + "itemicon");
            }
            else if (subCategory == "窗户")
            {
                // 添加装饰物
                zhuansi[2].GetComponent<Image>().sprite = await AAResources.LoadAssetAsync<Sprite>(itemId + "itemicon");
            }
            else if (subCategory == "墙面")
            {
                // 添加装饰物
                zhuansi[1].GetComponent<Image>().sprite = await AAResources.LoadAssetAsync<Sprite>(itemId + "itemicon");
            }
            else if (subCategory == "盆栽")
            {
                // 添加装饰物
                zhuansi[4].GetComponent<Image>().sprite = await AAResources.LoadAssetAsync<Sprite>(itemId + "itemicon");
            }
            else if (subCategory == "窝")
            {
                // 添加装饰物
                zhuansi[5].GetComponent<Image>().sprite = await AAResources.LoadAssetAsync<Sprite>(itemId + "itemicon");
            }
            else if (subCategory == "墙画")
            {
                // 添加装饰物
                zhuansi[7].GetComponent<Image>().sprite = await AAResources.LoadAssetAsync<Sprite>(itemId + "itemicon");
            }
            else if (subCategory == "饮水")
            {
                // 添加装饰物
                zhuansi[10].GetComponent<Image>().sprite = await AAResources.LoadAssetAsync<Sprite>(itemId + "itemicon");
            }
        }

        // 应用折扣
        private void ApplyDiscount(int discountValue)
        {
            Debug.Log($"应用折扣: {discountValue}%");
            // 这里可以实现折扣的逻辑
            // 例如临时降低商店中物品的价格
        }
    }
}
