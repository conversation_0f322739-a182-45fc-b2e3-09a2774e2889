using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace SuperScrollView
{
    public class NestedListViewTopToBottomDemoScript : MonoBehaviour
    {
        public LoopListView2 mLoopListView;
        public int mTotalDataCount = 5;
        DataSourceMgr<NestedItemData> mDataSourceMgr;
        // ButtonPanelNested mButtonPanel;         
        private void Awake()
        {
            Init();
        }
        // Use this for initialization
        public void Init()
        {
            ShowPurchasedItem();
            Debug.Log("Items.Count:" + Items.Count + "purchasedItemIds.Count:" + purchasedItemIds.Count);
            mDataSourceMgr = new DataSourceMgr<NestedItemData>(purchasedItemIds.Count);
            mLoopListView.InitListView(purchasedItemIds.Count, OnGetItemByIndex);
            InitButtonPanel();
        }

        public void Refresh()
        {
            ClearDictionary(); ShowPurchasedItem();
            mLoopListView.RefreshAllShownItem();

        }
        public void Refresh(int index)
        {
            // ClearDictionary();
            // ShowPurchasedItem();
            mLoopListView.RefreshItemByItemIndex(index);
            mLoopListView.ResetListView();
        }
        void InitButtonPanel()
        {
            // mButtonPanel = new ButtonPanelNested();
            // mButtonPanel.mLoopListView = mLoopListView;
            // mButtonPanel.mDataSourceMgr = mDataSourceMgr;
            // mButtonPanel.Start();
        }

        LoopListViewItem2 OnGetItemByIndex(LoopListView2 listView, int index)
        {

            Debug.Log("Items.Count1:" + Items.Count + "purchasedItemIds.Count1:" + purchasedItemIds.Count);
            Debug.Log("mDataSourceMgr.TotalItemCount:" + mDataSourceMgr.TotalItemCount);
            if (index < 0 || index >= purchasedItemIds.Count)
            {
                return null;
            }

            NestedItemData itemData = mDataSourceMgr.GetItemDataByIndex(index);
            if (itemData == null)
            {
                return null;
            }


            //get a new item. Every item can use a different prefab, the parameter of the NewListViewItem is the prefab’name. 
            //And all the prefabs should be listed in ItemPrefabList in LoopListView2 Inspector Setting
            LoopListViewItem2 item = listView.NewListViewItem("ItemPrefab");
            NestedLeftRightItem itemScript = item.GetComponent<NestedLeftRightItem>();
            if (item.IsInitHandlerCalled == false)
            {
                item.IsInitHandlerCalled = true;
                itemScript.Init();
            }
            itemData.purchasedItemIds = purchasedItemIds;
            itemScript.SetItemData(itemData, Items, purchasedItemIds);
            return item;
        }
        public void ClearDictionary()
        {
            Items.Clear();
            // purchasedItemIds.Clear();
        }
        Dictionary<string, string[]> Items = new Dictionary<string, string[]>();
        //List<string> Items = new List<string>();
        //Dictionary<string, int> Items = new Dictionary<string, int>();
        //Dictionary<string, int> Items = new Dictionary<string, int>();
        //Dictionary<string, int> Items = new Dictionary<string, int>();
        //Dictionary<string, int> Items = new Dictionary<string, int>();

        List<string> purchasedItemIds = new List<string>();
        private void ShowPurchasedItem()
        {

            purchasedItemIds = ItemManager.Instance.purchasedItems;

            if (purchasedItemIds.Count > 0)
            {
                foreach (string itemId in purchasedItemIds)
                {
                    // 获取物品数据
                    CatItemData itemData = ItemConfig.Instance.GetItemById(itemId);
                    if (itemData != null)
                    {
                        string category = itemData.category;
                        string subCategory = itemData.subCategory;
                        if (category == "食物")
                        {
                            //   Debug.Log("食物");
                            if (!Items.ContainsKey(category))
                            {

                                Items.Add(category, new string[] { itemId });
                            }
                            else
                            { // Get existing array
                                string[] existingArray = Items[category];

                                // Create new array with one more element
                                string[] newArray = new string[existingArray.Length + 1];

                                // Copy existing elements
                                existingArray.CopyTo(newArray, 0);

                                // Add new element at the end
                                newArray[existingArray.Length] = itemId;

                                // Replace the old array with the new one
                                Items[category] = newArray;

                            }

                        }
                        else if (category == "玩具")
                        {
                            // Debug.Log("玩具");
                            if (!Items.ContainsKey(category))
                            {

                                Items.Add(category, new string[] { itemId });
                            }
                            else
                            { // Get existing array
                                string[] existingArray = Items[category];

                                // Create new array with one more element
                                string[] newArray = new string[existingArray.Length + 1];

                                // Copy existing elements
                                existingArray.CopyTo(newArray, 0);

                                // Add new element at the end
                                newArray[existingArray.Length] = itemId;

                                // Replace the old array with the new one
                                Items[category] = newArray;

                            }



                        }
                        else if (category == "装饰")
                        {
                            // Debug.Log("装饰");

                            // if (!Items.ContainsKey(category + subCategory))
                            // {

                            //     Items.Add(category + subCategory, 1);
                            // }
                            // else
                            // {
                            //     Items[category + subCategory]++;
                            // }
                            if (!Items.ContainsKey(category + subCategory))
                            {

                                Items.Add(category + subCategory, new string[] { itemId });
                            }
                            else
                            { // Get existing array
                                string[] existingArray = Items[category + subCategory];

                                // Create new array with one more element
                                string[] newArray = new string[existingArray.Length + 1];

                                // Copy existing elements
                                existingArray.CopyTo(newArray, 0);

                                // Add new element at the end
                                newArray[existingArray.Length] = itemId;

                                // Replace the old array with the new one
                                Items[category + subCategory] = newArray;

                            }
                        }
                        else
                        {
                            // Debug.Log("其他");
                        }
                    }
                }
            }
            else
            {
                //   Debug.Log("没有购买的物品");
            }
        }

    }
}