using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading.Tasks;
using Feif.UIFramework;
using UnityEngine;
using UnityEngine.UI;
using ZM.UGUIPro;


namespace Feif.UI
{
    public class TiLiSaleWindowData : UIData
    {
    }

    [UIWindow]
    public class TiLiSaleWindow : UIComponent<TiLiSaleWindowData>
    {
        public ButtonPro buttonPro;
        public ButtonPro buttonPro1;
        public ButtonPro buttonclose;
        public Text text;
        public Text text1;

        protected override Task OnCreate()
        {
            return Task.CompletedTask;
        }

        protected override Task OnRefresh()
        {
            return Task.CompletedTask;
        }

        protected override void OnBind()
        {
            buttonclose.onClick.AddListener(() => { UIFrame.Hide<TiLiSaleWindow>(); });
            buttonPro.onClick.AddListener(() => { buttonProTip(); });
            buttonPro1.onClick.AddListener(() => { buttonProTip1(); });
        }

        protected override void OnUnbind()
        {

            buttonclose.onClick.RemoveListener(() => { UIFrame.Hide<TiLiSaleWindow>(); });
            buttonPro.onClick.RemoveListener(() => { buttonProTip(); });
            buttonPro1.onClick.RemoveListener(() => { buttonProTip1(); });
        }

        private void buttonProTip1()
        {
            Tilimanger.Instance.ConsumeStamina(-5);
            UIFrame.Hide<TiLiSaleWindow>();
        }

        private void buttonProTip()
        {
            Tilimanger.Instance.ConsumeStamina(-50);
        }

        protected override void OnShow()
        {
        }

        protected override void OnHide()
        {
        }

        protected override void OnDied()
        {
        }
    }
}